QT += core widgets multimedia multimediawidgets network

CONFIG += c++17

TARGET = AIVersion
TEMPLATE = app

# 定义应用程序信息
VERSION = 1.0.0
QMAKE_TARGET_COMPANY = "AI Vision Platform"
QMAKE_TARGET_PRODUCT = "AI Vision Platform"
QMAKE_TARGET_DESCRIPTION = "AI图像识别平台"
QMAKE_TARGET_COPYRIGHT = "Copyright 2024"

# 输出目录配置
CONFIG(debug, debug|release) {
    DESTDIR = $$PWD/build/debug
    OBJECTS_DIR = $$PWD/build/debug/obj
    MOC_DIR = $$PWD/build/debug/moc
    RCC_DIR = $$PWD/build/debug/rcc
    UI_DIR = $$PWD/build/debug/ui
}

CONFIG(release, debug|release) {
    DESTDIR = $$PWD/build/release
    OBJECTS_DIR = $$PWD/build/release/obj
    MOC_DIR = $$PWD/build/release/moc
    RCC_DIR = $$PWD/build/release/rcc
    UI_DIR = $$PWD/build/release/ui
}

# OpenCV配置
OPENCV_DIR = D:/opencv/build

# 包含路径
INCLUDEPATH += $$OPENCV_DIR/include

# 库路径
CONFIG(debug, debug|release) {
    LIBS += -L$$OPENCV_DIR/x64/vc16/lib \
            -lopencv_world4100d
}

CONFIG(release, debug|release) {
    LIBS += -L$$OPENCV_DIR/x64/vc16/lib \
            -lopencv_world4100
}

# 源文件
SOURCES += \
    main.cpp \
    mainwindow.cpp \
    OpenCVVisionEngine.cpp \
    VideoFrameCapture.cpp \
    GeminiAPIManager.cpp \
    DetectionOverlay.cpp

# 头文件
HEADERS += \
    mainwindow.h \
    OpenCVVisionEngine.h \
    VideoFrameCapture.h \
    GeminiAPIManager.h \
    DetectionOverlay.h

# 资源文件（如果有的话）
# RESOURCES += resources.qrc

# 其他文件（用于在Qt Creator中显示）
OTHER_FILES += \
    models/coco_labels.txt \
    models/YOLO_README.md \
    CMakeLists.txt \
    README.md

# 编译器标志
QMAKE_CXXFLAGS += -utf-8

# Windows特定配置
win32 {
    # 设置图标（如果有的话）
    # RC_ICONS = icon.ico
    
    # 设置版本信息
    RC_FILE = version.rc
    
    # 控制台应用程序（用于调试输出）
    CONFIG += console
}

# 预处理器定义
DEFINES += QT_DEPRECATED_WARNINGS

# 禁用特定警告
DEFINES += QT_NO_DEBUG_OUTPUT

# 如果是调试版本，启用调试输出
CONFIG(debug, debug|release) {
    DEFINES -= QT_NO_DEBUG_OUTPUT
    DEFINES += DEBUG_BUILD
}

# 确保模型文件被复制到输出目录
copydata.commands = $(COPY_DIR) $$shell_path($$PWD/models) $$shell_path($$DESTDIR/models)
first.depends = $(first) copydata
export(first.depends)
export(copydata.commands)
QMAKE_EXTRA_TARGETS += first copydata
