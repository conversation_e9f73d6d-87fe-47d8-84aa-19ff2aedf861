﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{B541E4E6-2DBC-3024-A6E9-97129AD46D3E}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>AIVisionPlatform</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\QT  pros\111\build\bin\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">AIVisionPlatform.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">AIVisionPlatform</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\QT  pros\111\build\bin\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">AIVisionPlatform.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">AIVisionPlatform</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\QT  pros\111\build\bin\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">AIVisionPlatform.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">AIVisionPlatform</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\QT  pros\111\build\bin\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">AIVisionPlatform.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">AIVisionPlatform</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\QT  pros\111\build\AIVisionPlatform_autogen\include_Debug;D:\QT  pros\111\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/opencv/build/include" /external:I "D:/QT/6.9.1/msvc2022_64/include/QtCore" /external:I "D:/QT/6.9.1/msvc2022_64/include" /external:I "D:/QT/6.9.1/msvc2022_64/mkspecs/win32-msvc" /external:I "D:/QT/6.9.1/msvc2022_64/include/QtWidgets" /external:I "D:/QT/6.9.1/msvc2022_64/include/QtGui" /external:I "D:/QT/6.9.1/msvc2022_64/include/QtMultimedia" /external:I "D:/QT/6.9.1/msvc2022_64/include/QtNetwork" /external:I "D:/QT/6.9.1/msvc2022_64/include/QtMultimediaWidgets" -Zc:__cplusplus -utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <ConformanceMode>true</ConformanceMode>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;QT_CORE_LIB;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_MULTIMEDIA_LIB;QT_NETWORK_LIB;QT_MULTIMEDIAWIDGETS_LIB;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;QT_CORE_LIB;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_MULTIMEDIA_LIB;QT_NETWORK_LIB;QT_MULTIMEDIAWIDGETS_LIB;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\QT  pros\111\build\AIVisionPlatform_autogen\include_Debug;D:\QT  pros\111\include;D:\opencv\build\include;D:\QT\6.9.1\msvc2022_64\include\QtCore;D:\QT\6.9.1\msvc2022_64\include;D:\QT\6.9.1\msvc2022_64\mkspecs\win32-msvc;D:\QT\6.9.1\msvc2022_64\include\QtWidgets;D:\QT\6.9.1\msvc2022_64\include\QtGui;D:\QT\6.9.1\msvc2022_64\include\QtMultimedia;D:\QT\6.9.1\msvc2022_64\include\QtNetwork;D:\QT\6.9.1\msvc2022_64\include\QtMultimediaWidgets;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\QT  pros\111\build\AIVisionPlatform_autogen\include_Debug;D:\QT  pros\111\include;D:\opencv\build\include;D:\QT\6.9.1\msvc2022_64\include\QtCore;D:\QT\6.9.1\msvc2022_64\include;D:\QT\6.9.1\msvc2022_64\mkspecs\win32-msvc;D:\QT\6.9.1\msvc2022_64\include\QtWidgets;D:\QT\6.9.1\msvc2022_64\include\QtGui;D:\QT\6.9.1\msvc2022_64\include\QtMultimedia;D:\QT\6.9.1\msvc2022_64\include\QtNetwork;D:\QT\6.9.1\msvc2022_64\include\QtMultimediaWidgets;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC for target AIVisionPlatform</Message>
      <Command>setlocal
cd "D:\QT  pros\111\build"
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\bin\cmake.exe -E cmake_autogen "D:/QT  pros/111/build/CMakeFiles/AIVisionPlatform_autogen.dir/AutogenInfo.json" Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>D:\QT\6.9.1\msvc2022_64\lib\Qt6MultimediaWidgetsd.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\QT\6.9.1\msvc2022_64\lib\Qt6Widgetsd.lib;D:\QT\6.9.1\msvc2022_64\lib\Qt6Multimediad.lib;D:\QT\6.9.1\msvc2022_64\lib\Qt6Networkd.lib;ws2_32.lib;D:\QT\6.9.1\msvc2022_64\lib\Qt6Guid.lib;D:\QT\6.9.1\msvc2022_64\lib\Qt6Cored.lib;mpr.lib;userenv.lib;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/QT  pros/111/build/Debug/AIVisionPlatform.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/QT  pros/111/build/bin/Debug/AIVisionPlatform.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\QT  pros\111\build\AIVisionPlatform_autogen\include_Release;D:\QT  pros\111\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/opencv/build/include" /external:I "D:/QT/6.9.1/msvc2022_64/include/QtCore" /external:I "D:/QT/6.9.1/msvc2022_64/include" /external:I "D:/QT/6.9.1/msvc2022_64/mkspecs/win32-msvc" /external:I "D:/QT/6.9.1/msvc2022_64/include/QtWidgets" /external:I "D:/QT/6.9.1/msvc2022_64/include/QtGui" /external:I "D:/QT/6.9.1/msvc2022_64/include/QtMultimedia" /external:I "D:/QT/6.9.1/msvc2022_64/include/QtNetwork" /external:I "D:/QT/6.9.1/msvc2022_64/include/QtMultimediaWidgets" -Zc:__cplusplus -utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ConformanceMode>true</ConformanceMode>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_MULTIMEDIA_LIB;QT_NETWORK_LIB;QT_MULTIMEDIAWIDGETS_LIB;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_MULTIMEDIA_LIB;QT_NETWORK_LIB;QT_MULTIMEDIAWIDGETS_LIB;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\QT  pros\111\build\AIVisionPlatform_autogen\include_Release;D:\QT  pros\111\include;D:\opencv\build\include;D:\QT\6.9.1\msvc2022_64\include\QtCore;D:\QT\6.9.1\msvc2022_64\include;D:\QT\6.9.1\msvc2022_64\mkspecs\win32-msvc;D:\QT\6.9.1\msvc2022_64\include\QtWidgets;D:\QT\6.9.1\msvc2022_64\include\QtGui;D:\QT\6.9.1\msvc2022_64\include\QtMultimedia;D:\QT\6.9.1\msvc2022_64\include\QtNetwork;D:\QT\6.9.1\msvc2022_64\include\QtMultimediaWidgets;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\QT  pros\111\build\AIVisionPlatform_autogen\include_Release;D:\QT  pros\111\include;D:\opencv\build\include;D:\QT\6.9.1\msvc2022_64\include\QtCore;D:\QT\6.9.1\msvc2022_64\include;D:\QT\6.9.1\msvc2022_64\mkspecs\win32-msvc;D:\QT\6.9.1\msvc2022_64\include\QtWidgets;D:\QT\6.9.1\msvc2022_64\include\QtGui;D:\QT\6.9.1\msvc2022_64\include\QtMultimedia;D:\QT\6.9.1\msvc2022_64\include\QtNetwork;D:\QT\6.9.1\msvc2022_64\include\QtMultimediaWidgets;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC for target AIVisionPlatform</Message>
      <Command>setlocal
cd "D:\QT  pros\111\build"
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\bin\cmake.exe -E cmake_autogen "D:/QT  pros/111/build/CMakeFiles/AIVisionPlatform_autogen.dir/AutogenInfo.json" Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>D:\QT\6.9.1\msvc2022_64\lib\Qt6MultimediaWidgets.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\QT\6.9.1\msvc2022_64\lib\Qt6Widgets.lib;D:\QT\6.9.1\msvc2022_64\lib\Qt6Multimedia.lib;D:\QT\6.9.1\msvc2022_64\lib\Qt6Network.lib;ws2_32.lib;D:\QT\6.9.1\msvc2022_64\lib\Qt6Gui.lib;D:\QT\6.9.1\msvc2022_64\lib\Qt6Core.lib;mpr.lib;userenv.lib;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/QT  pros/111/build/Release/AIVisionPlatform.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/QT  pros/111/build/bin/Release/AIVisionPlatform.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\QT  pros\111\build\AIVisionPlatform_autogen\include_MinSizeRel;D:\QT  pros\111\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/opencv/build/include" /external:I "D:/QT/6.9.1/msvc2022_64/include/QtCore" /external:I "D:/QT/6.9.1/msvc2022_64/include" /external:I "D:/QT/6.9.1/msvc2022_64/mkspecs/win32-msvc" /external:I "D:/QT/6.9.1/msvc2022_64/include/QtWidgets" /external:I "D:/QT/6.9.1/msvc2022_64/include/QtGui" /external:I "D:/QT/6.9.1/msvc2022_64/include/QtMultimedia" /external:I "D:/QT/6.9.1/msvc2022_64/include/QtNetwork" /external:I "D:/QT/6.9.1/msvc2022_64/include/QtMultimediaWidgets" -Zc:__cplusplus -utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ConformanceMode>true</ConformanceMode>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_MULTIMEDIA_LIB;QT_NETWORK_LIB;QT_MULTIMEDIAWIDGETS_LIB;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_MULTIMEDIA_LIB;QT_NETWORK_LIB;QT_MULTIMEDIAWIDGETS_LIB;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\QT  pros\111\build\AIVisionPlatform_autogen\include_MinSizeRel;D:\QT  pros\111\include;D:\opencv\build\include;D:\QT\6.9.1\msvc2022_64\include\QtCore;D:\QT\6.9.1\msvc2022_64\include;D:\QT\6.9.1\msvc2022_64\mkspecs\win32-msvc;D:\QT\6.9.1\msvc2022_64\include\QtWidgets;D:\QT\6.9.1\msvc2022_64\include\QtGui;D:\QT\6.9.1\msvc2022_64\include\QtMultimedia;D:\QT\6.9.1\msvc2022_64\include\QtNetwork;D:\QT\6.9.1\msvc2022_64\include\QtMultimediaWidgets;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\QT  pros\111\build\AIVisionPlatform_autogen\include_MinSizeRel;D:\QT  pros\111\include;D:\opencv\build\include;D:\QT\6.9.1\msvc2022_64\include\QtCore;D:\QT\6.9.1\msvc2022_64\include;D:\QT\6.9.1\msvc2022_64\mkspecs\win32-msvc;D:\QT\6.9.1\msvc2022_64\include\QtWidgets;D:\QT\6.9.1\msvc2022_64\include\QtGui;D:\QT\6.9.1\msvc2022_64\include\QtMultimedia;D:\QT\6.9.1\msvc2022_64\include\QtNetwork;D:\QT\6.9.1\msvc2022_64\include\QtMultimediaWidgets;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC for target AIVisionPlatform</Message>
      <Command>setlocal
cd "D:\QT  pros\111\build"
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\bin\cmake.exe -E cmake_autogen "D:/QT  pros/111/build/CMakeFiles/AIVisionPlatform_autogen.dir/AutogenInfo.json" MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>D:\QT\6.9.1\msvc2022_64\lib\Qt6MultimediaWidgets.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\QT\6.9.1\msvc2022_64\lib\Qt6Widgets.lib;D:\QT\6.9.1\msvc2022_64\lib\Qt6Multimedia.lib;D:\QT\6.9.1\msvc2022_64\lib\Qt6Network.lib;ws2_32.lib;D:\QT\6.9.1\msvc2022_64\lib\Qt6Gui.lib;D:\QT\6.9.1\msvc2022_64\lib\Qt6Core.lib;mpr.lib;userenv.lib;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/QT  pros/111/build/MinSizeRel/AIVisionPlatform.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/QT  pros/111/build/bin/MinSizeRel/AIVisionPlatform.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\QT  pros\111\build\AIVisionPlatform_autogen\include_RelWithDebInfo;D:\QT  pros\111\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/opencv/build/include" /external:I "D:/QT/6.9.1/msvc2022_64/include/QtCore" /external:I "D:/QT/6.9.1/msvc2022_64/include" /external:I "D:/QT/6.9.1/msvc2022_64/mkspecs/win32-msvc" /external:I "D:/QT/6.9.1/msvc2022_64/include/QtWidgets" /external:I "D:/QT/6.9.1/msvc2022_64/include/QtGui" /external:I "D:/QT/6.9.1/msvc2022_64/include/QtMultimedia" /external:I "D:/QT/6.9.1/msvc2022_64/include/QtNetwork" /external:I "D:/QT/6.9.1/msvc2022_64/include/QtMultimediaWidgets" -Zc:__cplusplus -utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ConformanceMode>true</ConformanceMode>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_MULTIMEDIA_LIB;QT_NETWORK_LIB;QT_MULTIMEDIAWIDGETS_LIB;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_MULTIMEDIA_LIB;QT_NETWORK_LIB;QT_MULTIMEDIAWIDGETS_LIB;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\QT  pros\111\build\AIVisionPlatform_autogen\include_RelWithDebInfo;D:\QT  pros\111\include;D:\opencv\build\include;D:\QT\6.9.1\msvc2022_64\include\QtCore;D:\QT\6.9.1\msvc2022_64\include;D:\QT\6.9.1\msvc2022_64\mkspecs\win32-msvc;D:\QT\6.9.1\msvc2022_64\include\QtWidgets;D:\QT\6.9.1\msvc2022_64\include\QtGui;D:\QT\6.9.1\msvc2022_64\include\QtMultimedia;D:\QT\6.9.1\msvc2022_64\include\QtNetwork;D:\QT\6.9.1\msvc2022_64\include\QtMultimediaWidgets;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\QT  pros\111\build\AIVisionPlatform_autogen\include_RelWithDebInfo;D:\QT  pros\111\include;D:\opencv\build\include;D:\QT\6.9.1\msvc2022_64\include\QtCore;D:\QT\6.9.1\msvc2022_64\include;D:\QT\6.9.1\msvc2022_64\mkspecs\win32-msvc;D:\QT\6.9.1\msvc2022_64\include\QtWidgets;D:\QT\6.9.1\msvc2022_64\include\QtGui;D:\QT\6.9.1\msvc2022_64\include\QtMultimedia;D:\QT\6.9.1\msvc2022_64\include\QtNetwork;D:\QT\6.9.1\msvc2022_64\include\QtMultimediaWidgets;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC for target AIVisionPlatform</Message>
      <Command>setlocal
cd "D:\QT  pros\111\build"
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\bin\cmake.exe -E cmake_autogen "D:/QT  pros/111/build/CMakeFiles/AIVisionPlatform_autogen.dir/AutogenInfo.json" RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>D:\QT\6.9.1\msvc2022_64\lib\Qt6MultimediaWidgets.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\QT\6.9.1\msvc2022_64\lib\Qt6Widgets.lib;D:\QT\6.9.1\msvc2022_64\lib\Qt6Multimedia.lib;D:\QT\6.9.1\msvc2022_64\lib\Qt6Network.lib;ws2_32.lib;D:\QT\6.9.1\msvc2022_64\lib\Qt6Gui.lib;D:\QT\6.9.1\msvc2022_64\lib\Qt6Core.lib;mpr.lib;userenv.lib;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/QT  pros/111/build/RelWithDebInfo/AIVisionPlatform.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/QT  pros/111/build/bin/RelWithDebInfo/AIVisionPlatform.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\QT  pros\111\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/QT  pros/111/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\bin\cmake.exe "-SD:/QT  pros/111" "-BD:/QT  pros/111/build" --check-stamp-file "D:/QT  pros/111/build/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeFindDependencyMacro.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeRCInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CheckCSourceCompiles.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CheckCXXCompilerFlag.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CheckCXXSourceCompiles.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CheckIncludeFile.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CheckLibraryExists.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPackageMessage.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindThreads.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindVulkan.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\GNUInstallDirs.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CheckCompilerFlag.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CheckSourceCompiles.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;D:\QT  pros\111\build\CMakeFiles\4.0.0\CMakeCCompiler.cmake;D:\QT  pros\111\build\CMakeFiles\4.0.0\CMakeCXXCompiler.cmake;D:\QT  pros\111\build\CMakeFiles\4.0.0\CMakeRCCompiler.cmake;D:\QT  pros\111\build\CMakeFiles\4.0.0\CMakeSystem.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\FindWrapAtomic.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\FindWrapVulkanHeaders.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\Qt6Config.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\Qt6Dependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\Qt6Targets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtFeature.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtFeatureCommon.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtInstallPaths.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigExtras.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreMacros.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiPlugins.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaMacros.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaPlugins.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QFFmpegMediaPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QFFmpegMediaPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QFFmpegMediaPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QFFmpegMediaPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QFFmpegMediaPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QWindowsMediaPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QWindowsMediaPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QWindowsMediaPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QWindowsMediaPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QWindowsMediaPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaPrivate\Qt6MultimediaPrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaPrivate\Qt6MultimediaPrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaPrivate\Qt6MultimediaPrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaPrivate\Qt6MultimediaPrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaPrivate\Qt6MultimediaPrivateDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaPrivate\Qt6MultimediaPrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaPrivate\Qt6MultimediaPrivateVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgetsPrivate\Qt6MultimediaWidgetsPrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgetsPrivate\Qt6MultimediaWidgetsPrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgetsPrivate\Qt6MultimediaWidgetsPrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgetsPrivate\Qt6MultimediaWidgetsPrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgetsPrivate\Qt6MultimediaWidgetsPrivateDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgetsPrivate\Qt6MultimediaWidgetsPrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgetsPrivate\Qt6MultimediaWidgetsPrivateVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkPlugins.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsMacros.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsPlugins.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateVersionlessAliasTargets.cmake;D:\opencv\build\OpenCVConfig-version.cmake;D:\opencv\build\OpenCVConfig.cmake;D:\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;D:\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;D:\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;D:\opencv\build\x64\vc16\lib\OpenCVModules.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\QT  pros\111\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/QT  pros/111/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\bin\cmake.exe "-SD:/QT  pros/111" "-BD:/QT  pros/111/build" --check-stamp-file "D:/QT  pros/111/build/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeFindDependencyMacro.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeRCInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CheckCSourceCompiles.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CheckCXXCompilerFlag.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CheckCXXSourceCompiles.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CheckIncludeFile.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CheckLibraryExists.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPackageMessage.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindThreads.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindVulkan.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\GNUInstallDirs.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CheckCompilerFlag.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CheckSourceCompiles.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;D:\QT  pros\111\build\CMakeFiles\4.0.0\CMakeCCompiler.cmake;D:\QT  pros\111\build\CMakeFiles\4.0.0\CMakeCXXCompiler.cmake;D:\QT  pros\111\build\CMakeFiles\4.0.0\CMakeRCCompiler.cmake;D:\QT  pros\111\build\CMakeFiles\4.0.0\CMakeSystem.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\FindWrapAtomic.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\FindWrapVulkanHeaders.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\Qt6Config.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\Qt6Dependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\Qt6Targets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtFeature.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtFeatureCommon.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtInstallPaths.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigExtras.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreMacros.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiPlugins.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaMacros.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaPlugins.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QFFmpegMediaPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QFFmpegMediaPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QFFmpegMediaPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QFFmpegMediaPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QFFmpegMediaPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QWindowsMediaPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QWindowsMediaPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QWindowsMediaPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QWindowsMediaPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QWindowsMediaPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaPrivate\Qt6MultimediaPrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaPrivate\Qt6MultimediaPrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaPrivate\Qt6MultimediaPrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaPrivate\Qt6MultimediaPrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaPrivate\Qt6MultimediaPrivateDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaPrivate\Qt6MultimediaPrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaPrivate\Qt6MultimediaPrivateVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgetsPrivate\Qt6MultimediaWidgetsPrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgetsPrivate\Qt6MultimediaWidgetsPrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgetsPrivate\Qt6MultimediaWidgetsPrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgetsPrivate\Qt6MultimediaWidgetsPrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgetsPrivate\Qt6MultimediaWidgetsPrivateDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgetsPrivate\Qt6MultimediaWidgetsPrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgetsPrivate\Qt6MultimediaWidgetsPrivateVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkPlugins.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsMacros.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsPlugins.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateVersionlessAliasTargets.cmake;D:\opencv\build\OpenCVConfig-version.cmake;D:\opencv\build\OpenCVConfig.cmake;D:\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;D:\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;D:\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;D:\opencv\build\x64\vc16\lib\OpenCVModules.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\QT  pros\111\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/QT  pros/111/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\bin\cmake.exe "-SD:/QT  pros/111" "-BD:/QT  pros/111/build" --check-stamp-file "D:/QT  pros/111/build/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeFindDependencyMacro.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeRCInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CheckCSourceCompiles.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CheckCXXCompilerFlag.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CheckCXXSourceCompiles.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CheckIncludeFile.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CheckLibraryExists.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPackageMessage.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindThreads.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindVulkan.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\GNUInstallDirs.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CheckCompilerFlag.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CheckSourceCompiles.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;D:\QT  pros\111\build\CMakeFiles\4.0.0\CMakeCCompiler.cmake;D:\QT  pros\111\build\CMakeFiles\4.0.0\CMakeCXXCompiler.cmake;D:\QT  pros\111\build\CMakeFiles\4.0.0\CMakeRCCompiler.cmake;D:\QT  pros\111\build\CMakeFiles\4.0.0\CMakeSystem.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\FindWrapAtomic.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\FindWrapVulkanHeaders.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\Qt6Config.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\Qt6Dependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\Qt6Targets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtFeature.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtFeatureCommon.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtInstallPaths.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigExtras.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreMacros.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiPlugins.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaMacros.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaPlugins.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QFFmpegMediaPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QFFmpegMediaPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QFFmpegMediaPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QFFmpegMediaPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QFFmpegMediaPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QWindowsMediaPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QWindowsMediaPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QWindowsMediaPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QWindowsMediaPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QWindowsMediaPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaPrivate\Qt6MultimediaPrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaPrivate\Qt6MultimediaPrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaPrivate\Qt6MultimediaPrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaPrivate\Qt6MultimediaPrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaPrivate\Qt6MultimediaPrivateDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaPrivate\Qt6MultimediaPrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaPrivate\Qt6MultimediaPrivateVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgetsPrivate\Qt6MultimediaWidgetsPrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgetsPrivate\Qt6MultimediaWidgetsPrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgetsPrivate\Qt6MultimediaWidgetsPrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgetsPrivate\Qt6MultimediaWidgetsPrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgetsPrivate\Qt6MultimediaWidgetsPrivateDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgetsPrivate\Qt6MultimediaWidgetsPrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgetsPrivate\Qt6MultimediaWidgetsPrivateVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkPlugins.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsMacros.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsPlugins.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateVersionlessAliasTargets.cmake;D:\opencv\build\OpenCVConfig-version.cmake;D:\opencv\build\OpenCVConfig.cmake;D:\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;D:\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;D:\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;D:\opencv\build\x64\vc16\lib\OpenCVModules.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\QT  pros\111\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/QT  pros/111/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\bin\cmake.exe "-SD:/QT  pros/111" "-BD:/QT  pros/111/build" --check-stamp-file "D:/QT  pros/111/build/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeFindDependencyMacro.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeRCInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CheckCSourceCompiles.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CheckCXXCompilerFlag.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CheckCXXSourceCompiles.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CheckIncludeFile.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CheckLibraryExists.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPackageMessage.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindThreads.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindVulkan.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\GNUInstallDirs.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CheckCompilerFlag.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CheckSourceCompiles.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;D:\QT  pros\111\build\CMakeFiles\4.0.0\CMakeCCompiler.cmake;D:\QT  pros\111\build\CMakeFiles\4.0.0\CMakeCXXCompiler.cmake;D:\QT  pros\111\build\CMakeFiles\4.0.0\CMakeRCCompiler.cmake;D:\QT  pros\111\build\CMakeFiles\4.0.0\CMakeSystem.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\FindWrapAtomic.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\FindWrapVulkanHeaders.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\Qt6Config.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\Qt6Dependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\Qt6Targets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtFeature.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtFeatureCommon.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtInstallPaths.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigExtras.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreMacros.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiPlugins.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaMacros.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaPlugins.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6MultimediaVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QFFmpegMediaPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QFFmpegMediaPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QFFmpegMediaPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QFFmpegMediaPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QFFmpegMediaPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QWindowsMediaPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QWindowsMediaPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QWindowsMediaPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QWindowsMediaPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Multimedia\Qt6QWindowsMediaPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaPrivate\Qt6MultimediaPrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaPrivate\Qt6MultimediaPrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaPrivate\Qt6MultimediaPrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaPrivate\Qt6MultimediaPrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaPrivate\Qt6MultimediaPrivateDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaPrivate\Qt6MultimediaPrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaPrivate\Qt6MultimediaPrivateVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgets\Qt6MultimediaWidgetsVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgetsPrivate\Qt6MultimediaWidgetsPrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgetsPrivate\Qt6MultimediaWidgetsPrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgetsPrivate\Qt6MultimediaWidgetsPrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgetsPrivate\Qt6MultimediaWidgetsPrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgetsPrivate\Qt6MultimediaWidgetsPrivateDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgetsPrivate\Qt6MultimediaWidgetsPrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6MultimediaWidgetsPrivate\Qt6MultimediaWidgetsPrivateVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkPlugins.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6NetworkVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsMacros.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsPlugins.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateVersionlessAliasTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-debug.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-relwithdebinfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateAdditionalTargetInfo.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfig.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersion.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersionImpl.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateTargets.cmake;D:\QT\6.9.1\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateVersionlessAliasTargets.cmake;D:\opencv\build\OpenCVConfig-version.cmake;D:\opencv\build\OpenCVConfig.cmake;D:\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;D:\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;D:\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;D:\opencv\build\x64\vc16\lib\OpenCVModules.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\QT  pros\111\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\QT  pros\111\build\AIVisionPlatform_autogen\mocs_compilation_Debug.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D:\QT  pros\111\src\core\OpenCVVisionEngine.cpp" />
    <ClCompile Include="D:\QT  pros\111\src\core\VideoFrameCapture.cpp" />
    <ClCompile Include="D:\QT  pros\111\src\main.cpp" />
    <ClCompile Include="D:\QT  pros\111\src\ui\DetectionOverlay.cpp" />
    <ClCompile Include="D:\QT  pros\111\src\ui\MainWindow.cpp" />
    <ClInclude Include="D:\QT  pros\111\include\DetectionOverlay.h" />
    <ClInclude Include="D:\QT  pros\111\include\MainWindow.h" />
    <ClInclude Include="D:\QT  pros\111\include\OpenCVVisionEngine.h" />
    <ClInclude Include="D:\QT  pros\111\include\VideoFrameCapture.h" />
    <ClCompile Include="D:\QT  pros\111\build\AIVisionPlatform_autogen\mocs_compilation_Release.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D:\QT  pros\111\build\AIVisionPlatform_autogen\mocs_compilation_MinSizeRel.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D:\QT  pros\111\build\AIVisionPlatform_autogen\mocs_compilation_RelWithDebInfo.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
    </ClCompile>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\QT  pros\111\build\ZERO_CHECK.vcxproj">
      <Project>{931BFEB7-1E4A-3C91-8017-C92E1C5A51BA}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>