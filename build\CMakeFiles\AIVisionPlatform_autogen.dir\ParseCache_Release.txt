# Generated by CMake. Changes will be overwritten.
D:/QT  pros/111/include/DetectionOverlay.h
 mmc:Q_OBJECT
 mdp:D:/QT  pros/111/include/DetectionOverlay.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QRectF
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QString
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QTimer
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q17memory.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20functional.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20iterator.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20memory.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20utility.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q23utility.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qabstracteventdispatcher.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qanystringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydata.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qassert.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbasictimer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearray.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qchar.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompare.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qconfig.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdatastream.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdeadlinetimer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdebug.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qelapsedtimer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qendian.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qeventloop.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qflags.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfloat16.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qforeach.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qhash.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiterable.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiterator.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qline.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlogging.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmalloc.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmargins.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmath.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmetatype.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qminmax.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qnamespace.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qnumeric.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobject.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qoverload.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qpair.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qpoint.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qrect.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qrefcount.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qset.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qshareddata.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsize.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qspan.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstring.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringlist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qswap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtextstream.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtimer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtnoop.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtresource.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qttranslation.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtversion.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtypes.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qvariant.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/QFont
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/QFontMetrics
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/QPainter
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qaction.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qbitmap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qbrush.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qcolor.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qcursor.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfont.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qicon.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qimage.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpainter.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpalette.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpen.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpixmap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpolygon.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qregion.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qrgb.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qrgba64.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtextoption.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtransform.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QWidget
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qwidget.h
D:/QT  pros/111/src/core/VideoFrameCapture.cpp
D:/QT  pros/111/include/MainWindow.h
 mmc:Q_OBJECT
 mdp:D:/QT  pros/111/include/DetectionOverlay.h
 mdp:D:/QT  pros/111/include/MainWindow.h
 mdp:D:/QT  pros/111/include/OpenCVVisionEngine.h
 mdp:D:/QT  pros/111/include/VideoFrameCapture.h
 mdp:D:/QT  pros/111/src/core/GeminiAPIManager.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QDateTime
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QDir
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QFileInfo
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QFlags
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QIODevice
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QJsonArray
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QJsonDocument
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QJsonObject
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QList
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QMap
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QMetaType
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QObject
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QRectF
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QSettings
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QSharedDataPointer
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QStandardPaths
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QString
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QStringList
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QTimer
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QUrl
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QVariant
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q17memory.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20functional.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20iterator.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20memory.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20utility.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q23utility.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q26numeric.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qabstracteventdispatcher.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qabstractitemmodel.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qanystringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydata.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qassert.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbasictimer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearray.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcalendar.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcborcommon.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcborvalue.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qchar.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompare.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qconfig.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcoreapplication.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcoreapplication_platform.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcoreevent.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcryptographichash.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdatastream.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdatetime.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdeadlinetimer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdebug.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdir.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdirlisting.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qelapsedtimer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qendian.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qeventloop.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfile.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfiledevice.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfileinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qflags.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfloat16.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qforeach.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qhash.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiodevice.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qitemselectionmodel.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiterable.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiterator.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonarray.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsondocument.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonobject.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonparseerror.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonvalue.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qline.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlocale.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlogging.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmalloc.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmargins.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmath.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmetatype.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qminmax.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qnamespace.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qnativeinterface.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qnumeric.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobject.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qoverload.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qpair.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qpoint.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qrect.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qrefcount.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qregularexpression.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qset.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsettings.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qshareddata.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsize.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qspan.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstandardpaths.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstring.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringlist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qswap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtextstream.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtimer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtimezone.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtnoop.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtresource.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qttranslation.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtversion.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtypes.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qurl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/quuid.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qvariant.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/QClipboard
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/QColor
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/QFont
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/QFontMetrics
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/QImage
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/QPainter
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/QPixmap
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qaction.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qbitmap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qbrush.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qclipboard.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qcolor.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qcursor.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfont.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qguiapplication.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qguiapplication_platform.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qicon.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qimage.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qinputmethod.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpainter.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpalette.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpen.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpicture.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpixmap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpolygon.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qregion.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qrgb.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qrgba64.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtextcursor.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtextdocument.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtextformat.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtextoption.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtransform.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qvalidator.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/QCamera
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/QCameraDevice
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/QImageCapture
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/QMediaCaptureSession
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/QMediaDevices
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/QMediaRecorder
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/QVideoFrame
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/QVideoSink
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qcamera.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qcameradevice.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qimagecapture.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qmediacapturesession.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qmediadevices.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qmediametadata.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qmediarecorder.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qtmultimedia-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qtmultimediaexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qtmultimediaglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qtvideo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qvideoframe.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qvideoframeformat.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qvideosink.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimediaWidgets/QVideoWidget
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimediaWidgets/qtmultimediawidgetsexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimediaWidgets/qtmultimediawidgetsglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimediaWidgets/qvideowidget.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/QNetworkAccessManager
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/QNetworkReply
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/QNetworkRequest
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/QSslConfiguration
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/QSslPreSharedKeyAuthenticator
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qabstractsocket.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qhostaddress.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qhttpheaders.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qnetworkaccessmanager.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qnetworkreply.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qnetworkrequest.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qssl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qsslcertificate.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qsslconfiguration.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qsslerror.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qsslpresharedkeyauthenticator.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qsslsocket.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qtcpsocket.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qtnetwork-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qtnetworkexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qtnetworkglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QApplication
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QDialog
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QFileDialog
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QFormLayout
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QFrame
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QGroupBox
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QHBoxLayout
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QInputDialog
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QLabel
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QLayout
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QListWidget
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QMainWindow
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QMenuBar
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QMessageBox
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QProgressDialog
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QPushButton
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QScrollArea
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QSlider
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QStatusBar
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QTextEdit
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QVBoxLayout
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QWidget
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qabstractbutton.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qabstractitemdelegate.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qabstractitemview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qabstractscrollarea.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qabstractslider.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qabstractspinbox.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qapplication.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qboxlayout.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qdialog.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qdialogbuttonbox.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qfiledialog.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qformlayout.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qframe.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qgridlayout.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qgroupbox.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qinputdialog.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qlabel.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qlayout.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qlayoutitem.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qlineedit.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qlistview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qlistwidget.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qmainwindow.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qmenu.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qmenubar.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qmessagebox.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qprogressdialog.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qpushbutton.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qrubberband.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qscrollarea.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qslider.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qstatusbar.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qstyle.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qstyleoption.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtabbar.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtabwidget.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtextedit.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qwidget.h
 mdp:D:/opencv/build/include/opencv2/calib3d.hpp
 mdp:D:/opencv/build/include/opencv2/core.hpp
 mdp:D:/opencv/build/include/opencv2/core/affine.hpp
 mdp:D:/opencv/build/include/opencv2/core/async.hpp
 mdp:D:/opencv/build/include/opencv2/core/base.hpp
 mdp:D:/opencv/build/include/opencv2/core/bufferpool.hpp
 mdp:D:/opencv/build/include/opencv2/core/check.hpp
 mdp:D:/opencv/build/include/opencv2/core/cuda.hpp
 mdp:D:/opencv/build/include/opencv2/core/cuda.inl.hpp
 mdp:D:/opencv/build/include/opencv2/core/cuda_types.hpp
 mdp:D:/opencv/build/include/opencv2/core/cv_cpu_dispatch.h
 mdp:D:/opencv/build/include/opencv2/core/cvdef.h
 mdp:D:/opencv/build/include/opencv2/core/cvstd.hpp
 mdp:D:/opencv/build/include/opencv2/core/cvstd.inl.hpp
 mdp:D:/opencv/build/include/opencv2/core/cvstd_wrapper.hpp
 mdp:D:/opencv/build/include/opencv2/core/fast_math.hpp
 mdp:D:/opencv/build/include/opencv2/core/hal/interface.h
 mdp:D:/opencv/build/include/opencv2/core/mat.hpp
 mdp:D:/opencv/build/include/opencv2/core/mat.inl.hpp
 mdp:D:/opencv/build/include/opencv2/core/matx.hpp
 mdp:D:/opencv/build/include/opencv2/core/matx.inl.hpp
 mdp:D:/opencv/build/include/opencv2/core/neon_utils.hpp
 mdp:D:/opencv/build/include/opencv2/core/operations.hpp
 mdp:D:/opencv/build/include/opencv2/core/optim.hpp
 mdp:D:/opencv/build/include/opencv2/core/ovx.hpp
 mdp:D:/opencv/build/include/opencv2/core/persistence.hpp
 mdp:D:/opencv/build/include/opencv2/core/saturate.hpp
 mdp:D:/opencv/build/include/opencv2/core/traits.hpp
 mdp:D:/opencv/build/include/opencv2/core/types.hpp
 mdp:D:/opencv/build/include/opencv2/core/utility.hpp
 mdp:D:/opencv/build/include/opencv2/core/utils/logger.defines.hpp
 mdp:D:/opencv/build/include/opencv2/core/utils/logger.hpp
 mdp:D:/opencv/build/include/opencv2/core/utils/logtag.hpp
 mdp:D:/opencv/build/include/opencv2/core/version.hpp
 mdp:D:/opencv/build/include/opencv2/core/vsx_utils.hpp
 mdp:D:/opencv/build/include/opencv2/dnn.hpp
 mdp:D:/opencv/build/include/opencv2/dnn/dict.hpp
 mdp:D:/opencv/build/include/opencv2/dnn/dnn.hpp
 mdp:D:/opencv/build/include/opencv2/dnn/dnn.inl.hpp
 mdp:D:/opencv/build/include/opencv2/dnn/layer.hpp
 mdp:D:/opencv/build/include/opencv2/dnn/utils/inference_engine.hpp
 mdp:D:/opencv/build/include/opencv2/dnn/version.hpp
 mdp:D:/opencv/build/include/opencv2/features2d.hpp
 mdp:D:/opencv/build/include/opencv2/flann.hpp
 mdp:D:/opencv/build/include/opencv2/flann/all_indices.h
 mdp:D:/opencv/build/include/opencv2/flann/allocator.h
 mdp:D:/opencv/build/include/opencv2/flann/any.h
 mdp:D:/opencv/build/include/opencv2/flann/autotuned_index.h
 mdp:D:/opencv/build/include/opencv2/flann/composite_index.h
 mdp:D:/opencv/build/include/opencv2/flann/config.h
 mdp:D:/opencv/build/include/opencv2/flann/defines.h
 mdp:D:/opencv/build/include/opencv2/flann/dist.h
 mdp:D:/opencv/build/include/opencv2/flann/dynamic_bitset.h
 mdp:D:/opencv/build/include/opencv2/flann/flann_base.hpp
 mdp:D:/opencv/build/include/opencv2/flann/general.h
 mdp:D:/opencv/build/include/opencv2/flann/ground_truth.h
 mdp:D:/opencv/build/include/opencv2/flann/heap.h
 mdp:D:/opencv/build/include/opencv2/flann/hierarchical_clustering_index.h
 mdp:D:/opencv/build/include/opencv2/flann/index_testing.h
 mdp:D:/opencv/build/include/opencv2/flann/kdtree_index.h
 mdp:D:/opencv/build/include/opencv2/flann/kdtree_single_index.h
 mdp:D:/opencv/build/include/opencv2/flann/kmeans_index.h
 mdp:D:/opencv/build/include/opencv2/flann/linear_index.h
 mdp:D:/opencv/build/include/opencv2/flann/logger.h
 mdp:D:/opencv/build/include/opencv2/flann/lsh_index.h
 mdp:D:/opencv/build/include/opencv2/flann/lsh_table.h
 mdp:D:/opencv/build/include/opencv2/flann/matrix.h
 mdp:D:/opencv/build/include/opencv2/flann/miniflann.hpp
 mdp:D:/opencv/build/include/opencv2/flann/nn_index.h
 mdp:D:/opencv/build/include/opencv2/flann/params.h
 mdp:D:/opencv/build/include/opencv2/flann/random.h
 mdp:D:/opencv/build/include/opencv2/flann/result_set.h
 mdp:D:/opencv/build/include/opencv2/flann/sampling.h
 mdp:D:/opencv/build/include/opencv2/flann/saving.h
 mdp:D:/opencv/build/include/opencv2/flann/timer.h
 mdp:D:/opencv/build/include/opencv2/highgui.hpp
 mdp:D:/opencv/build/include/opencv2/imgcodecs.hpp
 mdp:D:/opencv/build/include/opencv2/imgproc.hpp
 mdp:D:/opencv/build/include/opencv2/imgproc/segmentation.hpp
 mdp:D:/opencv/build/include/opencv2/ml.hpp
 mdp:D:/opencv/build/include/opencv2/ml/ml.inl.hpp
 mdp:D:/opencv/build/include/opencv2/objdetect.hpp
 mdp:D:/opencv/build/include/opencv2/objdetect/aruco_board.hpp
 mdp:D:/opencv/build/include/opencv2/objdetect/aruco_detector.hpp
 mdp:D:/opencv/build/include/opencv2/objdetect/aruco_dictionary.hpp
 mdp:D:/opencv/build/include/opencv2/objdetect/barcode.hpp
 mdp:D:/opencv/build/include/opencv2/objdetect/charuco_detector.hpp
 mdp:D:/opencv/build/include/opencv2/objdetect/detection_based_tracker.hpp
 mdp:D:/opencv/build/include/opencv2/objdetect/face.hpp
 mdp:D:/opencv/build/include/opencv2/objdetect/graphical_code_detector.hpp
 mdp:D:/opencv/build/include/opencv2/opencv.hpp
 mdp:D:/opencv/build/include/opencv2/opencv_modules.hpp
 mdp:D:/opencv/build/include/opencv2/photo.hpp
 mdp:D:/opencv/build/include/opencv2/stitching.hpp
 mdp:D:/opencv/build/include/opencv2/stitching/detail/blenders.hpp
 mdp:D:/opencv/build/include/opencv2/stitching/detail/camera.hpp
 mdp:D:/opencv/build/include/opencv2/stitching/detail/exposure_compensate.hpp
 mdp:D:/opencv/build/include/opencv2/stitching/detail/matchers.hpp
 mdp:D:/opencv/build/include/opencv2/stitching/detail/motion_estimators.hpp
 mdp:D:/opencv/build/include/opencv2/stitching/detail/seam_finders.hpp
 mdp:D:/opencv/build/include/opencv2/stitching/detail/util.hpp
 mdp:D:/opencv/build/include/opencv2/stitching/detail/util_inl.hpp
 mdp:D:/opencv/build/include/opencv2/stitching/detail/warpers.hpp
 mdp:D:/opencv/build/include/opencv2/stitching/detail/warpers_inl.hpp
 mdp:D:/opencv/build/include/opencv2/stitching/warpers.hpp
 mdp:D:/opencv/build/include/opencv2/video.hpp
 mdp:D:/opencv/build/include/opencv2/video/background_segm.hpp
 mdp:D:/opencv/build/include/opencv2/video/tracking.hpp
 mdp:D:/opencv/build/include/opencv2/videoio.hpp
D:/QT  pros/111/include/OpenCVVisionEngine.h
 mmc:Q_OBJECT
 mdp:D:/QT  pros/111/include/OpenCVVisionEngine.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QObject
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QRectF
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QString
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QStringList
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QTimer
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q17memory.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20functional.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20iterator.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20memory.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20utility.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q23utility.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qabstracteventdispatcher.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qanystringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydata.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qassert.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbasictimer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearray.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qchar.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompare.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qconfig.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdatastream.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdeadlinetimer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qelapsedtimer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qeventloop.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qflags.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfloat16.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qforeach.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiterable.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiterator.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qline.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlogging.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmalloc.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmargins.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmath.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmetatype.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qminmax.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qnamespace.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qnumeric.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobject.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qoverload.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qpair.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qpoint.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qrect.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qrefcount.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsize.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qspan.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstring.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringlist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qswap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtimer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtnoop.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtresource.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qttranslation.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtversion.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtypes.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/QColor
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/QImage
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qcolor.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qimage.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpolygon.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qregion.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qrgb.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qrgba64.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtransform.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:D:/opencv/build/include/opencv2/calib3d.hpp
 mdp:D:/opencv/build/include/opencv2/core.hpp
 mdp:D:/opencv/build/include/opencv2/core/affine.hpp
 mdp:D:/opencv/build/include/opencv2/core/async.hpp
 mdp:D:/opencv/build/include/opencv2/core/base.hpp
 mdp:D:/opencv/build/include/opencv2/core/bufferpool.hpp
 mdp:D:/opencv/build/include/opencv2/core/check.hpp
 mdp:D:/opencv/build/include/opencv2/core/cuda.hpp
 mdp:D:/opencv/build/include/opencv2/core/cuda.inl.hpp
 mdp:D:/opencv/build/include/opencv2/core/cuda_types.hpp
 mdp:D:/opencv/build/include/opencv2/core/cv_cpu_dispatch.h
 mdp:D:/opencv/build/include/opencv2/core/cvdef.h
 mdp:D:/opencv/build/include/opencv2/core/cvstd.hpp
 mdp:D:/opencv/build/include/opencv2/core/cvstd.inl.hpp
 mdp:D:/opencv/build/include/opencv2/core/cvstd_wrapper.hpp
 mdp:D:/opencv/build/include/opencv2/core/fast_math.hpp
 mdp:D:/opencv/build/include/opencv2/core/hal/interface.h
 mdp:D:/opencv/build/include/opencv2/core/mat.hpp
 mdp:D:/opencv/build/include/opencv2/core/mat.inl.hpp
 mdp:D:/opencv/build/include/opencv2/core/matx.hpp
 mdp:D:/opencv/build/include/opencv2/core/matx.inl.hpp
 mdp:D:/opencv/build/include/opencv2/core/neon_utils.hpp
 mdp:D:/opencv/build/include/opencv2/core/operations.hpp
 mdp:D:/opencv/build/include/opencv2/core/optim.hpp
 mdp:D:/opencv/build/include/opencv2/core/ovx.hpp
 mdp:D:/opencv/build/include/opencv2/core/persistence.hpp
 mdp:D:/opencv/build/include/opencv2/core/saturate.hpp
 mdp:D:/opencv/build/include/opencv2/core/traits.hpp
 mdp:D:/opencv/build/include/opencv2/core/types.hpp
 mdp:D:/opencv/build/include/opencv2/core/utility.hpp
 mdp:D:/opencv/build/include/opencv2/core/utils/logger.defines.hpp
 mdp:D:/opencv/build/include/opencv2/core/utils/logger.hpp
 mdp:D:/opencv/build/include/opencv2/core/utils/logtag.hpp
 mdp:D:/opencv/build/include/opencv2/core/version.hpp
 mdp:D:/opencv/build/include/opencv2/core/vsx_utils.hpp
 mdp:D:/opencv/build/include/opencv2/dnn.hpp
 mdp:D:/opencv/build/include/opencv2/dnn/dict.hpp
 mdp:D:/opencv/build/include/opencv2/dnn/dnn.hpp
 mdp:D:/opencv/build/include/opencv2/dnn/dnn.inl.hpp
 mdp:D:/opencv/build/include/opencv2/dnn/layer.hpp
 mdp:D:/opencv/build/include/opencv2/dnn/utils/inference_engine.hpp
 mdp:D:/opencv/build/include/opencv2/dnn/version.hpp
 mdp:D:/opencv/build/include/opencv2/features2d.hpp
 mdp:D:/opencv/build/include/opencv2/flann.hpp
 mdp:D:/opencv/build/include/opencv2/flann/all_indices.h
 mdp:D:/opencv/build/include/opencv2/flann/allocator.h
 mdp:D:/opencv/build/include/opencv2/flann/any.h
 mdp:D:/opencv/build/include/opencv2/flann/autotuned_index.h
 mdp:D:/opencv/build/include/opencv2/flann/composite_index.h
 mdp:D:/opencv/build/include/opencv2/flann/config.h
 mdp:D:/opencv/build/include/opencv2/flann/defines.h
 mdp:D:/opencv/build/include/opencv2/flann/dist.h
 mdp:D:/opencv/build/include/opencv2/flann/dynamic_bitset.h
 mdp:D:/opencv/build/include/opencv2/flann/flann_base.hpp
 mdp:D:/opencv/build/include/opencv2/flann/general.h
 mdp:D:/opencv/build/include/opencv2/flann/ground_truth.h
 mdp:D:/opencv/build/include/opencv2/flann/heap.h
 mdp:D:/opencv/build/include/opencv2/flann/hierarchical_clustering_index.h
 mdp:D:/opencv/build/include/opencv2/flann/index_testing.h
 mdp:D:/opencv/build/include/opencv2/flann/kdtree_index.h
 mdp:D:/opencv/build/include/opencv2/flann/kdtree_single_index.h
 mdp:D:/opencv/build/include/opencv2/flann/kmeans_index.h
 mdp:D:/opencv/build/include/opencv2/flann/linear_index.h
 mdp:D:/opencv/build/include/opencv2/flann/logger.h
 mdp:D:/opencv/build/include/opencv2/flann/lsh_index.h
 mdp:D:/opencv/build/include/opencv2/flann/lsh_table.h
 mdp:D:/opencv/build/include/opencv2/flann/matrix.h
 mdp:D:/opencv/build/include/opencv2/flann/miniflann.hpp
 mdp:D:/opencv/build/include/opencv2/flann/nn_index.h
 mdp:D:/opencv/build/include/opencv2/flann/params.h
 mdp:D:/opencv/build/include/opencv2/flann/random.h
 mdp:D:/opencv/build/include/opencv2/flann/result_set.h
 mdp:D:/opencv/build/include/opencv2/flann/sampling.h
 mdp:D:/opencv/build/include/opencv2/flann/saving.h
 mdp:D:/opencv/build/include/opencv2/flann/timer.h
 mdp:D:/opencv/build/include/opencv2/highgui.hpp
 mdp:D:/opencv/build/include/opencv2/imgcodecs.hpp
 mdp:D:/opencv/build/include/opencv2/imgproc.hpp
 mdp:D:/opencv/build/include/opencv2/imgproc/segmentation.hpp
 mdp:D:/opencv/build/include/opencv2/ml.hpp
 mdp:D:/opencv/build/include/opencv2/ml/ml.inl.hpp
 mdp:D:/opencv/build/include/opencv2/objdetect.hpp
 mdp:D:/opencv/build/include/opencv2/objdetect/aruco_board.hpp
 mdp:D:/opencv/build/include/opencv2/objdetect/aruco_detector.hpp
 mdp:D:/opencv/build/include/opencv2/objdetect/aruco_dictionary.hpp
 mdp:D:/opencv/build/include/opencv2/objdetect/barcode.hpp
 mdp:D:/opencv/build/include/opencv2/objdetect/charuco_detector.hpp
 mdp:D:/opencv/build/include/opencv2/objdetect/detection_based_tracker.hpp
 mdp:D:/opencv/build/include/opencv2/objdetect/face.hpp
 mdp:D:/opencv/build/include/opencv2/objdetect/graphical_code_detector.hpp
 mdp:D:/opencv/build/include/opencv2/opencv.hpp
 mdp:D:/opencv/build/include/opencv2/opencv_modules.hpp
 mdp:D:/opencv/build/include/opencv2/photo.hpp
 mdp:D:/opencv/build/include/opencv2/stitching.hpp
 mdp:D:/opencv/build/include/opencv2/stitching/detail/blenders.hpp
 mdp:D:/opencv/build/include/opencv2/stitching/detail/camera.hpp
 mdp:D:/opencv/build/include/opencv2/stitching/detail/exposure_compensate.hpp
 mdp:D:/opencv/build/include/opencv2/stitching/detail/matchers.hpp
 mdp:D:/opencv/build/include/opencv2/stitching/detail/motion_estimators.hpp
 mdp:D:/opencv/build/include/opencv2/stitching/detail/seam_finders.hpp
 mdp:D:/opencv/build/include/opencv2/stitching/detail/util.hpp
 mdp:D:/opencv/build/include/opencv2/stitching/detail/util_inl.hpp
 mdp:D:/opencv/build/include/opencv2/stitching/detail/warpers.hpp
 mdp:D:/opencv/build/include/opencv2/stitching/detail/warpers_inl.hpp
 mdp:D:/opencv/build/include/opencv2/stitching/warpers.hpp
 mdp:D:/opencv/build/include/opencv2/video.hpp
 mdp:D:/opencv/build/include/opencv2/video/background_segm.hpp
 mdp:D:/opencv/build/include/opencv2/video/tracking.hpp
 mdp:D:/opencv/build/include/opencv2/videoio.hpp
D:/QT  pros/111/src/core/OpenCVVisionEngine.cpp
D:/QT  pros/111/src/main.cpp
D:/QT  pros/111/src/ui/DetectionOverlay.cpp
D:/QT  pros/111/src/ui/MainWindow.cpp
D:/QT  pros/111/include/VideoFrameCapture.h
 mmc:Q_OBJECT
 mdp:D:/QT  pros/111/include/VideoFrameCapture.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QObject
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QRectF
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QTimer
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q17memory.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20functional.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20iterator.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20memory.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20utility.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q23utility.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qabstracteventdispatcher.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qanystringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydata.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qassert.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbasictimer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearray.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qchar.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompare.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qconfig.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdatastream.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdeadlinetimer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qelapsedtimer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qendian.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qeventloop.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qflags.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfloat16.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qforeach.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiterable.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiterator.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qline.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlogging.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmalloc.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmargins.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmath.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmetatype.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qminmax.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qnamespace.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qnumeric.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobject.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qoverload.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qpair.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qpoint.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qrect.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qrefcount.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qshareddata.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsize.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qspan.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstring.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringlist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qswap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtimer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtnoop.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtresource.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qttranslation.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtversion.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtypes.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/QImage
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/QPainter
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qbrush.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qcolor.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfont.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qimage.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpainter.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpen.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpixmap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpolygon.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qregion.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qrgb.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qrgba64.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtextoption.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtransform.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/QVideoFrame
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/QVideoSink
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qtmultimedia-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qtmultimediaexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qtmultimediaglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qtvideo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qvideoframe.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qvideoframeformat.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtMultimedia/qvideosink.h
D:/QT  pros/111/src/core/GeminiAPIManager.h
 mmc:Q_OBJECT
 mdp:D:/QT  pros/111/src/core/GeminiAPIManager.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QFlags
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QIODevice
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QJsonArray
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QJsonDocument
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QJsonObject
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QList
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QMetaType
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QObject
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QSettings
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QSharedDataPointer
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QString
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QUrl
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QVariant
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q17memory.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20functional.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20iterator.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20memory.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20utility.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q23utility.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q26numeric.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qanystringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydata.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qassert.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearray.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcalendar.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcborcommon.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcborvalue.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qchar.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompare.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qconfig.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcryptographichash.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdatastream.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdatetime.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdebug.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qendian.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qflags.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfloat16.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qforeach.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qhash.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiodevice.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiterable.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiterator.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonarray.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsondocument.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonobject.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonparseerror.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonvalue.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qline.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlocale.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlogging.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmalloc.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmargins.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmath.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmetatype.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qminmax.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qnamespace.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qnumeric.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobject.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qoverload.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qpair.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qpoint.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qrect.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qrefcount.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qregularexpression.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qset.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsettings.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qshareddata.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsize.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qspan.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstring.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringlist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qswap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtextstream.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtnoop.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtresource.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qttranslation.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtversion.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtypes.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qurl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/quuid.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qvariant.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/QImage
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qcolor.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qimage.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpolygon.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qregion.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qrgb.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qrgba64.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtransform.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/QNetworkAccessManager
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/QNetworkReply
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/QNetworkRequest
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/QSslConfiguration
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/QSslPreSharedKeyAuthenticator
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qabstractsocket.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qhostaddress.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qhttpheaders.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qnetworkaccessmanager.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qnetworkreply.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qnetworkrequest.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qssl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qsslcertificate.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qsslconfiguration.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qsslerror.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qsslpresharedkeyauthenticator.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qsslsocket.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qtcpsocket.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qtnetwork-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qtnetworkexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qtnetworkglobal.h
D:/QT  pros/111/src/core/GeminiAPIManager.cpp
