#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QTextEdit>
#include <QMenuBar>
#include <QStatusBar>
#include <QTimer>
#include <QInputDialog>
#include <QMessageBox>
#include <QCamera>
#include <QMediaCaptureSession>
#include <QVideoWidget>
#include <QCameraDevice>
#include <QMediaDevices>
#include <QGroupBox>
#include <QFileDialog>
#include <QSlider>
#include <QFormLayout>
#include <QListWidget>
#include <QDialog>
#include <QFileInfo>
#include <QDateTime>
#include <QApplication>
#include <QImageCapture>
#include <QMediaRecorder>
#include <QStandardPaths>
#include <QDir>
#include <QDateTime>
#include <QUrl>
#include <QProgressDialog>
#include <QPixmap>
#include <QScrollArea>
#include <QFrame>
#include <QPainter>
#include <QFontMetrics>
#include <QMap>
#include <QClipboard>
#include "DetectionOverlay.h"
#include "OpenCVVisionEngine.h"
#include "VideoFrameCapture.h"
#include "GeminiAPIManager.h"

QT_BEGIN_NAMESPACE
namespace Ui {
class MainWindow;
}
QT_END_NAMESPACE

/**
 * @brief AI图像识别平台主窗口
 * 
 * 提供摄像头控制、图像捕获、视频录制和AI识别功能
 */
class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    // 摄像头控制槽函数
    void openCamera();
    void closeCamera();
    void startInference();
    void captureImage();
    void saveImageAs();
    void toggleRecording();
    void startRecording();
    void stopRecording();
    void updatePerformanceDisplay();
    void switchVisionMode();
    void onOpenCVDetectionReady(const std::vector<OpenCVDetection>& detections, const QImage& image);
    void onOpenCVStatsUpdated(const OpenCVVisionEngine::ProcessingStats& stats);

    // YOLO模型管理槽函数 (用于摄像头实时检测)
    void selectYOLOModel();
    void configureYOLOParameters();
    void showYOLOModelInfo();
    
    // Gemini API管理槽函数
    void configureGeminiAPI();
    void showGeminiAPIInfo();

    // 图片上传和分析槽函数
    void uploadAndAnalyzeImage();
    void analyzeImageWithGemini();  // 上传图片使用Gemini分析
    void analyzeImageWithYOLO();    // 保留用于其他功能
    void batchAnalyzeImages();
    
    // Gemini分析结果处理
    void onGeminiAnalysisCompleted(const GeminiAnalysisResult& result);
    void onGeminiAnalysisError(const QString& errorMessage);
    void onGeminiAnalysisProgress(const QString& status);

private:
    void setupUI();
    void setupCamera();
    void setupImageCapture();
    void setupVideoRecording();
    void setupPerformanceMonitor();
    void setupOpenCVVision();
    void setupFrameCapture();
    void setupGeminiAPI();
    QImage createTestImage();

private:
    Ui::MainWindow *ui;
    
    // 多媒体组件
    QCamera* camera;
    QMediaCaptureSession* captureSession;
    QVideoWidget* videoWidget;
    QImageCapture* imageCapture;
    QMediaRecorder* mediaRecorder;
    
    // UI组件
    QPushButton* openCameraButton;
    QPushButton* startInferenceButton;
    QPushButton* recordButton;
    QPushButton* uploadImageButton;
    QPushButton* batchAnalyzeButton;
    QTextEdit* performanceText;
    DetectionOverlay* detectionOverlay;
    QLabel* imageDisplayLabel;
    QScrollArea* imageScrollArea;

    // OpenCV组件
    OpenCVVisionEngine* openCVEngine;
    QTimer* processingTimer;

    // 视频帧捕获
    VideoFrameCapture* frameCapture;

    // Gemini API组件
    GeminiAPIManager* geminiManager;
    
    // 检测结果显示窗口
    QLabel* detectionImageLabel;
    
    // 当前分析的图像
    QImage currentAnalysisImage;
};

#endif // MAINWINDOW_H
