#include "../../include/OpenCVVisionEngine.h"
#include <QDebug>
#include <QElapsedTimer>
#include <QRandomGenerator>
#include <QColor>
#include <QFile>
#include <QTextStream>
#include <QDir>
#include <QFileInfo>
#include <cmath>

// OpenCV工具函数
cv::Mat qImageToCvMat(const QImage& inImage) {
    QImage swapped = inImage.rgbSwapped();
    return cv::Mat(swapped.height(), swapped.width(), CV_8UC3,
                   (void*)swapped.constBits(), swapped.bytesPerLine()).clone();
}

QImage cvMatToQImage(const cv::Mat& inMat) {
    switch (inMat.type()) {
        case CV_8UC4: {
            QImage image(inMat.data, inMat.cols, inMat.rows, inMat.step, QImage::Format_ARGB32);
            return image.rgbSwapped();
        }
        case CV_8UC3: {
            QImage image(inMat.data, inMat.cols, inMat.rows, inMat.step, QImage::Format_RGB888);
            return image.rgbSwapped();
        }
        case CV_8UC1: {
            QImage image(inMat.data, inMat.cols, inMat.rows, inMat.step, QImage::Format_Grayscale8);
            return image;
        }
    }
    return QImage();
}

OpenCVVisionEngine::OpenCVVisionEngine(QObject* parent)
    : QObject(parent)
    , currentMode_(FaceDetection)
    , processingEnabled_(false)
    , detectionThreshold_(0.3f)
    , minObjectSize_(30)
    , maxObjectSize_(300)
    , faceClassifier_(nullptr)
    , backgroundSubtractor_(nullptr)
    , tracker_(nullptr)
    , yoloNet_(nullptr)
    , yoloConfidenceThreshold_(0.25f)
    , yoloNMSThreshold_(0.45f)
    , yoloInputSize_(640)
{
    qDebug() << "OpenCV Vision Engine initialized with real OpenCV" << CV_VERSION;
    initializeClassifiers();
    initializeYOLO();
}

OpenCVVisionEngine::~OpenCVVisionEngine() {
    // 清理OpenCV对象
    if (faceClassifier_) {
        delete static_cast<cv::CascadeClassifier*>(faceClassifier_);
        faceClassifier_ = nullptr;
    }

    if (backgroundSubtractor_) {
        delete static_cast<cv::BackgroundSubtractorMOG2*>(backgroundSubtractor_);
        backgroundSubtractor_ = nullptr;
    }

    if (yoloNet_) {
        delete static_cast<cv::dnn::Net*>(yoloNet_);
        yoloNet_ = nullptr;
    }

    qDebug() << "OpenCV Vision Engine destroyed";
}

void OpenCVVisionEngine::setVisionMode(VisionMode mode) {
    if (currentMode_ != mode) {
        currentMode_ = mode;
        qDebug() << "Vision mode changed to:" << getCurrentModeDescription();
        resetStats();
    }
}

QStringList OpenCVVisionEngine::getAvailableModes() {
    return {
        "人脸检测",
        "运动检测",
        "边缘检测",
        "颜色检测",
        "特征点检测",
        "物体跟踪",
        "轮廓检测",
        "物体检测 (YOLO)"
    };
}

void OpenCVVisionEngine::setProcessingEnabled(bool enabled) {
    if (processingEnabled_ != enabled) {
        processingEnabled_ = enabled;
        emit processingStatusChanged(enabled);
        qDebug() << "OpenCV processing" << (enabled ? "enabled" : "disabled");
    }
}

std::vector<OpenCVDetection> OpenCVVisionEngine::processFrame(const QImage& image) {
    if (!processingEnabled_ || image.isNull()) {
        return {};
    }
    
    QElapsedTimer timer;
    timer.start();
    
    std::vector<OpenCVDetection> detections;
    
    // 根据当前模式处理图像
    switch (currentMode_) {
        case FaceDetection:
            detections = detectFaces(image);
            break;
        case MotionDetection:
            detections = detectMotion(image);
            break;
        case EdgeDetection:
            detections = detectEdges(image);
            break;
        case ColorDetection:
            detections = detectColors(image);
            break;
        case FeatureDetection:
            detections = detectFeatures(image);
            break;
        case ObjectTracking:
            detections = trackObjects(image);
            break;
        case ContourDetection:
            detections = detectContours(image);
            break;
        case ObjectDetection:
            detections = detectYOLOObjects(image);
            break;
    }
    
    // 更新统计信息
    float processingTime = timer.elapsed();
    updateStats(processingTime);
    
    // 发射结果信号
    emit detectionsReady(detections, image);
    
    return detections;
}

void OpenCVVisionEngine::resetStats() {
    stats_ = ProcessingStats();
}

QString OpenCVVisionEngine::getCurrentModeDescription() const {
    switch (currentMode_) {
        case FaceDetection: return "人脸检测 - 检测图像中的人脸";
        case MotionDetection: return "运动检测 - 检测移动的物体";
        case EdgeDetection: return "边缘检测 - 检测图像边缘和轮廓";
        case ColorDetection: return "颜色检测 - 检测特定颜色区域";
        case FeatureDetection: return "特征点检测 - 检测关键特征点";
        case ObjectTracking: return "物体跟踪 - 跟踪选定的物体";
        case ContourDetection: return "轮廓检测 - 检测物体轮廓";
        case ObjectDetection: return "物体检测 - 使用YOLO检测多种物体";
        default: return "未知模式";
    }
}

std::vector<OpenCVDetection> OpenCVVisionEngine::detectFaces(const QImage& image) {
    std::vector<OpenCVDetection> detections;

    if (image.isNull()) {
        return detections;
    }

    try {
        // 转换QImage到cv::Mat
        QImage rgbImage = image.convertToFormat(QImage::Format_RGB888);
        cv::Mat cvImage(rgbImage.height(), rgbImage.width(), CV_8UC3,
                       (void*)rgbImage.constBits(), rgbImage.bytesPerLine());
        cv::Mat cvImageClone = cvImage.clone();

        cv::Mat grayImage;
        cv::cvtColor(cvImageClone, grayImage, cv::COLOR_RGB2GRAY);

        // 使用Haar级联分类器进行人脸检测
        cv::CascadeClassifier* classifier = static_cast<cv::CascadeClassifier*>(faceClassifier_);

        if (classifier && !classifier->empty()) {
            std::vector<cv::Rect> faces;
            classifier->detectMultiScale(grayImage, faces, 1.1, 3, 0,
                                       cv::Size(minObjectSize_, minObjectSize_));

            // 转换检测结果
            for (size_t i = 0; i < faces.size(); ++i) {
                const cv::Rect& face = faces[i];

                // 转换为归一化坐标
                float x = float(face.x) / cvImageClone.cols;
                float y = float(face.y) / cvImageClone.rows;
                float w = float(face.width) / cvImageClone.cols;
                float h = float(face.height) / cvImageClone.rows;

                QRectF faceRect(x, y, w, h);
                float confidence = 0.85f;

                detections.emplace_back("face",
                                       QString("人脸 #%1").arg(i + 1),
                                       confidence,
                                       faceRect,
                                       QColor(0, 255, 0));
            }

            qDebug() << "Real OpenCV face detection found" << faces.size() << "faces";
        } else {
            qDebug() << "Face classifier not loaded, trying to reload...";
            initializeClassifiers();
        }
    } catch (const cv::Exception& e) {
        qDebug() << "OpenCV face detection error:" << e.what();
    }

    return detections;
}

std::vector<OpenCVDetection> OpenCVVisionEngine::detectMotion(const QImage& image) {
    std::vector<OpenCVDetection> detections;
    
    // 模拟运动检测
    auto* rng = QRandomGenerator::global();
    
    // 随机生成运动区域
    int numMotions = rng->bounded(0, 3);
    
    for (int i = 0; i < numMotions; ++i) {
        float x = rng->generateDouble() * 0.8f;
        float y = rng->generateDouble() * 0.8f;
        float w = 0.05f + rng->generateDouble() * 0.15f;
        float h = 0.05f + rng->generateDouble() * 0.15f;
        
        QRectF motionRect(x, y, w, h);
        float confidence = 0.4f + rng->generateDouble() * 0.6f;
        
        detections.emplace_back("motion", 
                               QString("运动区域 #%1").arg(i + 1), 
                               confidence, 
                               motionRect, 
                               QColor(255, 0, 0)); // 红色
    }
    
    return detections;
}

std::vector<OpenCVDetection> OpenCVVisionEngine::detectEdges(const QImage& image) {
    std::vector<OpenCVDetection> detections;

    if (image.isNull()) {
        return detections;
    }

    // 真实的边缘检测算法（简化版Sobel算子）
    QImage grayImage = image.convertToFormat(QImage::Format_Grayscale8);

    int edgeCount = 0;
    const int threshold = 50; // 边缘阈值

    // Sobel边缘检测
    for (int y = 1; y < grayImage.height() - 1; y += 2) { // 每2个像素采样
        for (int x = 1; x < grayImage.width() - 1; x += 2) {
            // 获取3x3邻域的像素值
            int p1 = qGray(grayImage.pixel(x-1, y-1));
            int p2 = qGray(grayImage.pixel(x, y-1));
            int p3 = qGray(grayImage.pixel(x+1, y-1));
            int p4 = qGray(grayImage.pixel(x-1, y));
            int p6 = qGray(grayImage.pixel(x+1, y));
            int p7 = qGray(grayImage.pixel(x-1, y+1));
            int p8 = qGray(grayImage.pixel(x, y+1));
            int p9 = qGray(grayImage.pixel(x+1, y+1));

            // Sobel算子
            int gx = (p3 + 2*p6 + p9) - (p1 + 2*p4 + p7);
            int gy = (p1 + 2*p2 + p3) - (p7 + 2*p8 + p9);

            int magnitude = sqrt(gx*gx + gy*gy);

            if (magnitude > threshold) {
                edgeCount++;
            }
        }
    }

    // 计算边缘密度
    int totalPixels = (grayImage.width() / 2) * (grayImage.height() / 2);
    float edgeDensity = float(edgeCount) / totalPixels;
    float confidence = qMin(1.0f, edgeDensity * 10.0f);

    QString description = QString("检测到 %1 条边缘 (密度: %2%)")
                         .arg(edgeCount)
                         .arg(QString::number(edgeDensity * 100, 'f', 1));

    detections.emplace_back("edges",
                           description,
                           confidence,
                           QRectF(0, 0, 1, 1), // 全图
                           QColor(0, 0, 255)); // 蓝色

    qDebug() << "Real edge detection found" << edgeCount << "edges, density:" << edgeDensity;

    return detections;
}

std::vector<OpenCVDetection> OpenCVVisionEngine::detectColors(const QImage& image) {
    std::vector<OpenCVDetection> detections;

    if (image.isNull()) {
        return detections;
    }

    // 真实的颜色检测算法
    const int minRegionSize = 100; // 最小区域像素数
    const int threshold = 50; // 颜色阈值

    // 定义要检测的颜色
    struct ColorTarget {
        QString name;
        QColor color;
        QRgb targetRgb;
    };

    QList<ColorTarget> targets = {
        {"红色区域", Qt::red, qRgb(255, 0, 0)},
        {"绿色区域", Qt::green, qRgb(0, 255, 0)},
        {"蓝色区域", Qt::blue, qRgb(0, 0, 255)},
        {"黄色区域", Qt::yellow, qRgb(255, 255, 0)},
        {"紫色区域", Qt::magenta, qRgb(255, 0, 255)}
    };

    // 扫描图像寻找颜色区域
    for (const auto& target : targets) {
        QList<QPoint> colorPixels;

        // 扫描图像像素
        for (int y = 0; y < image.height(); y += 4) { // 每4个像素采样一次，提高性能
            for (int x = 0; x < image.width(); x += 4) {
                QRgb pixel = image.pixel(x, y);

                // 计算颜色距离
                int rDiff = abs(qRed(pixel) - qRed(target.targetRgb));
                int gDiff = abs(qGreen(pixel) - qGreen(target.targetRgb));
                int bDiff = abs(qBlue(pixel) - qBlue(target.targetRgb));

                if (rDiff < threshold && gDiff < threshold && bDiff < threshold) {
                    colorPixels.append(QPoint(x, y));
                }
            }
        }

        // 如果找到足够的颜色像素，创建检测结果
        if (colorPixels.size() > minRegionSize / 16) { // 考虑采样率
            // 计算边界框
            int minX = image.width(), maxX = 0;
            int minY = image.height(), maxY = 0;

            for (const QPoint& p : colorPixels) {
                minX = qMin(minX, p.x());
                maxX = qMax(maxX, p.x());
                minY = qMin(minY, p.y());
                maxY = qMax(maxY, p.y());
            }

            // 转换为归一化坐标
            float x = float(minX) / image.width();
            float y = float(minY) / image.height();
            float w = float(maxX - minX) / image.width();
            float h = float(maxY - minY) / image.height();

            QRectF colorRect(x, y, w, h);
            float confidence = qMin(1.0f, float(colorPixels.size()) / 1000.0f);

            detections.emplace_back("color",
                                   target.name,
                                   confidence,
                                   colorRect,
                                   target.color);
        }
    }

    qDebug() << "Real color detection found" << detections.size() << "color regions";

    return detections;
}

std::vector<OpenCVDetection> OpenCVVisionEngine::detectFeatures(const QImage& image) {
    std::vector<OpenCVDetection> detections;
    
    // 模拟特征点检测
    auto* rng = QRandomGenerator::global();
    
    int numFeatures = 10 + rng->bounded(20);
    
    for (int i = 0; i < numFeatures && i < 5; ++i) { // 只显示前5个
        float x = rng->generateDouble();
        float y = rng->generateDouble();
        float size = 0.01f + rng->generateDouble() * 0.02f;
        
        QRectF featureRect(x - size/2, y - size/2, size, size);
        float confidence = 0.7f + rng->generateDouble() * 0.3f;
        
        detections.emplace_back("feature", 
                               QString("特征点 #%1").arg(i + 1), 
                               confidence, 
                               featureRect, 
                               QColor(255, 255, 0)); // 黄色
    }
    
    return detections;
}

std::vector<OpenCVDetection> OpenCVVisionEngine::trackObjects(const QImage& image) {
    std::vector<OpenCVDetection> detections;
    
    // 模拟物体跟踪
    auto* rng = QRandomGenerator::global();
    
    // 假设跟踪1-2个物体
    int numTracked = rng->bounded(1, 3);
    
    for (int i = 0; i < numTracked; ++i) {
        float x = rng->generateDouble() * 0.7f;
        float y = rng->generateDouble() * 0.7f;
        float w = 0.1f + rng->generateDouble() * 0.2f;
        float h = 0.1f + rng->generateDouble() * 0.2f;
        
        QRectF trackRect(x, y, w, h);
        float confidence = 0.8f + rng->generateDouble() * 0.2f;
        
        detections.emplace_back("tracking", 
                               QString("跟踪目标 #%1").arg(i + 1), 
                               confidence, 
                               trackRect, 
                               QColor(255, 0, 255)); // 洋红色
    }
    
    return detections;
}

std::vector<OpenCVDetection> OpenCVVisionEngine::detectContours(const QImage& image) {
    std::vector<OpenCVDetection> detections;
    
    // 模拟轮廓检测
    auto* rng = QRandomGenerator::global();
    
    int numContours = rng->bounded(2, 6);
    
    for (int i = 0; i < numContours; ++i) {
        float x = rng->generateDouble() * 0.8f;
        float y = rng->generateDouble() * 0.8f;
        float w = 0.08f + rng->generateDouble() * 0.15f;
        float h = 0.08f + rng->generateDouble() * 0.15f;
        
        QRectF contourRect(x, y, w, h);
        float confidence = 0.6f + rng->generateDouble() * 0.4f;
        
        detections.emplace_back("contour", 
                               QString("轮廓 #%1").arg(i + 1), 
                               confidence, 
                               contourRect, 
                               QColor(0, 255, 255)); // 青色
    }
    
    return detections;
}

void OpenCVVisionEngine::initializeClassifiers() {
    try {
        // 加载人脸检测分类器
        cv::CascadeClassifier* faceClassifier = new cv::CascadeClassifier();

        // 尝试多个可能的分类器路径
        QStringList classifierPaths = {
            "haarcascade_frontalface_default.xml",  // 本地文件
            "D:/opencv/build/etc/haarcascades/haarcascade_frontalface_default.xml",
            "D:/opencv/build/etc/haarcascades/haarcascade_frontalface_alt.xml",
            "D:/opencv/sources/data/haarcascades/haarcascade_frontalface_alt.xml"
        };

        bool loaded = false;
        for (const QString& path : classifierPaths) {
            if (faceClassifier->load(path.toStdString())) {
                faceClassifier_ = faceClassifier;
                qDebug() << "Successfully loaded face classifier from" << path;
                loaded = true;
                break;
            }
        }

        if (!loaded) {
            delete faceClassifier;
            faceClassifier_ = nullptr;
            qDebug() << "Failed to load face classifier from all paths";
        }

        // 创建背景减除器
        auto bgSub = cv::createBackgroundSubtractorMOG2(500, 16, false);
        backgroundSubtractor_ = bgSub.get();

        qDebug() << "OpenCV classifiers initialized";
        qDebug() << "OpenCV version:" << CV_VERSION;
    } catch (const cv::Exception& e) {
        qDebug() << "Error initializing OpenCV classifiers:" << e.what();
    }
}

void OpenCVVisionEngine::updateStats(float processingTime) {
    stats_.totalFrames++;
    stats_.processedFrames++;

    // 计算平均处理时间
    static float totalTime = 0.0f;
    totalTime += processingTime;
    stats_.averageProcessingTime = totalTime / stats_.processedFrames;

    // 每10帧发射一次统计更新
    if (stats_.processedFrames % 10 == 0) {
        emit statsUpdated(stats_);
    }
}

// ==================== YOLO相关实现 ====================

bool OpenCVVisionEngine::initializeYOLO() {
    try {
        qDebug() << "Initializing YOLO...";

        // 加载COCO类别标签
        QString labelsPath = "D:/AI_Models/coco_labels.txt";
        if (!loadYOLOClassNames(labelsPath)) {
            qDebug() << "Failed to load YOLO class names from" << labelsPath;
            return false;
        }

        // 尝试加载默认YOLO模型
        QString defaultModelPath = "D:/AI_Models/yolo/yolov5/yolov5s.onnx";
        if (loadYOLOModel(defaultModelPath)) {
            qDebug() << "YOLO initialized successfully with model:" << defaultModelPath;
            return true;
        } else {
            qDebug() << "YOLO initialization completed but no model loaded";
            return true; // 初始化成功，但没有模型
        }
    } catch (const cv::Exception& e) {
        qDebug() << "Error initializing YOLO:" << e.what();
        return false;
    }
}

bool OpenCVVisionEngine::loadYOLOClassNames(const QString& labelsPath) {
    try {
        QFile file(labelsPath);
        if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
            qDebug() << "Cannot open labels file:" << labelsPath;
            return false;
        }

        yoloClassNames_.clear();
        QTextStream in(&file);
        while (!in.atEnd()) {
            QString line = in.readLine().trimmed();
            if (!line.isEmpty()) {
                yoloClassNames_.push_back(line);
            }
        }

        qDebug() << "Loaded" << yoloClassNames_.size() << "YOLO class names";
        return !yoloClassNames_.empty();
    } catch (const std::exception& e) {
        qDebug() << "Error loading YOLO class names:" << e.what();
        return false;
    }
}

bool OpenCVVisionEngine::loadYOLOModel(const QString& modelPath) {
    try {
        QFile file(modelPath);
        if (!file.exists()) {
            qDebug() << "YOLO model file does not exist:" << modelPath;
            return false;
        }

        qDebug() << "Loading YOLO model from:" << modelPath;

        // 清理现有的网络
        if (yoloNet_) {
            delete static_cast<cv::dnn::Net*>(yoloNet_);
            yoloNet_ = nullptr;
        }

        // 加载ONNX模型
        cv::dnn::Net* net = new cv::dnn::Net();
        *net = cv::dnn::readNetFromONNX(modelPath.toStdString());

        if (net->empty()) {
            delete net;
            qDebug() << "Failed to load YOLO model from:" << modelPath;
            return false;
        }

        // 设置计算后端
        net->setPreferableBackend(cv::dnn::DNN_BACKEND_OPENCV);
        net->setPreferableTarget(cv::dnn::DNN_TARGET_CPU);

        yoloNet_ = net;
        currentYOLOModelPath_ = modelPath;

        qDebug() << "YOLO model loaded successfully:" << modelPath;
        return true;
    } catch (const cv::Exception& e) {
        qDebug() << "Error loading YOLO model:" << e.what();
        return false;
    }
}

QStringList OpenCVVisionEngine::getAvailableYOLOModels() const {
    QStringList models;

    // 扫描D:/AI_Models/yolo目录下的模型文件
    QDir yoloDir("D:/AI_Models/yolo");
    if (yoloDir.exists()) {
        QStringList filters;
        filters << "*.onnx" << "*.pb" << "*.weights";

        QFileInfoList files = yoloDir.entryInfoList(filters, QDir::Files, QDir::Name);
        for (const QFileInfo& file : files) {
            models << file.absoluteFilePath();
        }

        // 递归搜索子目录
        QStringList subDirs = yoloDir.entryList(QDir::Dirs | QDir::NoDotAndDotDot);
        for (const QString& subDir : subDirs) {
            QDir subDirectory(yoloDir.absoluteFilePath(subDir));
            QFileInfoList subFiles = subDirectory.entryInfoList(filters, QDir::Files, QDir::Name);
            for (const QFileInfo& file : subFiles) {
                models << file.absoluteFilePath();
            }
        }
    }

    return models;
}

std::vector<OpenCVDetection> OpenCVVisionEngine::detectYOLOObjects(const QImage& image) {
    std::vector<OpenCVDetection> detections;

    if (image.isNull()) {
        return detections;
    }

    // 如果YOLO模型未加载，使用模拟检测进行测试
    if (!yoloNet_ || yoloClassNames_.empty()) {
        qDebug() << "YOLO model not loaded, using simulated detection";
        return simulateYOLODetection(image);
    }

    try {
        // 转换QImage到cv::Mat
        QImage rgbImage = image.convertToFormat(QImage::Format_RGB888);
        cv::Mat cvImage(rgbImage.height(), rgbImage.width(), CV_8UC3,
                       (void*)rgbImage.constBits(), rgbImage.bytesPerLine());
        cv::Mat cvImageClone = cvImage.clone();

        // 预处理图像
        cv::Mat blob = preprocessYOLOImage(cvImageClone);

        // 设置网络输入
        cv::dnn::Net* net = static_cast<cv::dnn::Net*>(yoloNet_);
        net->setInput(blob);

        // 前向推理
        std::vector<cv::Mat> outputs;
        net->forward(outputs, net->getUnconnectedOutLayersNames());

        // 后处理
        detections = postprocessYOLOOutput(outputs, cvImageClone.size());

        qDebug() << "YOLO detected" << detections.size() << "objects";

    } catch (const cv::Exception& e) {
        qDebug() << "Error in YOLO detection:" << e.what();
        emit errorOccurred(QString("YOLO检测错误: %1").arg(e.what()));
    }

    return detections;
}

cv::Mat OpenCVVisionEngine::preprocessYOLOImage(const cv::Mat& image) {
    cv::Mat blob;

    // 创建blob：缩放到640x640，归一化到[0,1]，转换为NCHW格式
    cv::dnn::blobFromImage(image, blob, 1.0/255.0, cv::Size(yoloInputSize_, yoloInputSize_),
                          cv::Scalar(0,0,0), true, false, CV_32F);

    return blob;
}

std::vector<OpenCVDetection> OpenCVVisionEngine::postprocessYOLOOutput(
    const std::vector<cv::Mat>& outputs, const cv::Size& imageSize) {

    std::vector<OpenCVDetection> detections;

    if (outputs.empty()) {
        return detections;
    }

    std::vector<int> classIds;
    std::vector<float> confidences;
    std::vector<cv::Rect> boxes;

    // 解析YOLO输出
    for (size_t outputIdx = 0; outputIdx < outputs.size(); ++outputIdx) {
        const cv::Mat& output = outputs[outputIdx];
        qDebug() << "YOLO output" << outputIdx << "shape: [" << output.rows << "x" << output.cols << "] dims:" << output.dims;

        // YOLOv5输出通常是 [1, 25200, 85] 格式，需要重新整形
        cv::Mat reshapedOutput;
        if (output.dims == 3) {
            // 如果是3维，重新整形为2维 [detections, attributes]
            int totalDetections = output.size[1];
            int attributes = output.size[2];
            reshapedOutput = output.reshape(1, totalDetections);
            qDebug() << "Reshaped YOLO output to:" << totalDetections << "x" << attributes;
        } else {
            reshapedOutput = output;
        }

        const float* data = (float*)reshapedOutput.data;
        int numDetections = reshapedOutput.rows;
        int numAttributes = reshapedOutput.cols;

        qDebug() << "Processing" << numDetections << "detections with" << numAttributes << "attributes each";

        for (int i = 0; i < numDetections; ++i) {
            const float* row = data + i * numAttributes;

            // YOLO输出格式: [x, y, w, h, confidence, class_scores...]
            float confidence = row[4];

            if (confidence >= 0.1f) { // 临时降低阈值进行调试
                // 找到最高分数的类别
                cv::Mat scores = reshapedOutput.row(i).colRange(5, numAttributes);
                cv::Point classIdPoint;
                double maxClassScore;
                cv::minMaxLoc(scores, 0, &maxClassScore, 0, &classIdPoint);

                if (maxClassScore >= 0.1f) { // 临时降低阈值
                    qDebug() << "Found detection: confidence=" << confidence
                             << "maxClassScore=" << maxClassScore
                             << "classId=" << classIdPoint.x;

                    // 转换边界框坐标
                    float centerX = row[0];
                    float centerY = row[1];
                    float width = row[2];
                    float height = row[3];

                    int left = static_cast<int>((centerX - width / 2) * imageSize.width / yoloInputSize_);
                    int top = static_cast<int>((centerY - height / 2) * imageSize.height / yoloInputSize_);
                    int boxWidth = static_cast<int>(width * imageSize.width / yoloInputSize_);
                    int boxHeight = static_cast<int>(height * imageSize.height / yoloInputSize_);

                    classIds.push_back(classIdPoint.x);
                    confidences.push_back(static_cast<float>(maxClassScore));
                    boxes.push_back(cv::Rect(left, top, boxWidth, boxHeight));
                }
            }
        }
    }

    // 非极大值抑制
    std::vector<int> indices;
    cv::dnn::NMSBoxes(boxes, confidences, yoloConfidenceThreshold_, yoloNMSThreshold_, indices);

    // 创建检测结果
    for (int idx : indices) {
        if (idx >= 0 && idx < static_cast<int>(boxes.size()) &&
            classIds[idx] >= 0 && classIds[idx] < static_cast<int>(yoloClassNames_.size())) {

            const cv::Rect& box = boxes[idx];

            // 转换为归一化坐标
            float x = static_cast<float>(box.x) / imageSize.width;
            float y = static_cast<float>(box.y) / imageSize.height;
            float w = static_cast<float>(box.width) / imageSize.width;
            float h = static_cast<float>(box.height) / imageSize.height;

            QRectF normalizedBox(x, y, w, h);

            QString className = yoloClassNames_[classIds[idx]];
            QString description = QString("%1").arg(className);

            // 根据类别设置颜色
            QColor color = QColor::fromHsv((classIds[idx] * 137) % 360, 255, 255);

            detections.emplace_back("yolo_object", description, confidences[idx],
                                   normalizedBox, color);
        }
    }

    return detections;
}

std::vector<OpenCVDetection> OpenCVVisionEngine::simulateYOLODetection(const QImage& image) {
    std::vector<OpenCVDetection> detections;

    // 模拟YOLO检测结果，用于测试UI集成
    auto* rng = QRandomGenerator::global();

    // 模拟检测到的常见物体
    QStringList commonObjects = {
        "person", "car", "bicycle", "dog", "cat", "chair", "bottle", "laptop", "cell phone", "book"
    };

    int numDetections = rng->bounded(1, 4); // 1-3个检测结果

    for (int i = 0; i < numDetections; ++i) {
        // 随机选择一个物体类别
        QString objectClass = commonObjects[rng->bounded(commonObjects.size())];

        // 生成随机边界框
        float x = rng->generateDouble() * 0.7f;
        float y = rng->generateDouble() * 0.7f;
        float w = 0.1f + rng->generateDouble() * 0.2f;
        float h = 0.1f + rng->generateDouble() * 0.2f;

        QRectF boundingBox(x, y, w, h);

        // 生成随机置信度
        float confidence = 0.5f + rng->generateDouble() * 0.5f;

        // 根据物体类别设置颜色
        QColor color;
        if (objectClass == "person") color = QColor(255, 0, 0);      // 红色
        else if (objectClass == "car") color = QColor(0, 255, 0);   // 绿色
        else if (objectClass == "bicycle") color = QColor(0, 0, 255); // 蓝色
        else color = QColor::fromHsv(rng->bounded(360), 255, 255);   // 随机颜色

        QString description = QString("%1 (模拟)").arg(objectClass);

        detections.emplace_back("yolo_simulated", description, confidence, boundingBox, color);
    }

    return detections;
}
