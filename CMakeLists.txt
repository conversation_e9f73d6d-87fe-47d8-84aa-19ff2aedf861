cmake_minimum_required(VERSION 3.16)
project(AIVisionPlatform)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找Qt6组件
find_package(Qt6 REQUIRED COMPONENTS Core Widgets Multimedia MultimediaWidgets)

# 启用Qt MOC
set(CMAKE_AUTOMOC ON)

# OpenCV配置
set(OpenCV_DIR "D:/opencv/build")
find_package(OpenCV REQUIRED)

message(STATUS "OpenCV library status:")
message(STATUS "    version: ${OpenCV_VERSION}")
message(STATUS "    libraries: ${OpenCV_LIBS}")
message(STATUS "    include path: ${OpenCV_INCLUDE_DIRS}")

# 包含目录
include_directories(${CMAKE_SOURCE_DIR}/include)
include_directories(${OpenCV_INCLUDE_DIRS})

# 收集源文件


file(GLOB_RECURSE SOURCES
    "src/*.cpp"
    "src/*.h"
)

# 确保包含所有核心源文件
list(APPEND SOURCES
    ${CMAKE_SOURCE_DIR}/src/core/OpenCVVisionEngine.cpp
    ${CMAKE_SOURCE_DIR}/src/core/VideoFrameCapture.cpp
)

file(GLOB_RECURSE HEADERS
    "include/*.h"
)

# 创建可执行文件
add_executable(AIVisionPlatform
    ${SOURCES}
    ${HEADERS}
)

# 链接库
target_link_libraries(AIVisionPlatform PRIVATE
    Qt6::Core
    Qt6::Widgets
    Qt6::Multimedia
    Qt6::MultimediaWidgets
    ${OpenCV_LIBS}
)

# 设置输出目录
set_target_properties(AIVisionPlatform PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# 设置调试信息
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(AIVisionPlatform PRIVATE DEBUG_BUILD)
endif()
