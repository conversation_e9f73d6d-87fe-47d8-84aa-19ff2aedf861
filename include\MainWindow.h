#pragma once

#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QTextEdit>
#include <QMenuBar>
#include <QStatusBar>
#include <QTimer>
#include <QInputDialog>
#include <QMessageBox>
#include <QCamera>
#include <QMediaCaptureSession>
#include <QVideoWidget>
#include <QCameraDevice>
#include <QMediaDevices>
#include <QGroupBox>
#include <QFileDialog>
#include <QImageCapture>
#include <QMediaRecorder>
#include <QStandardPaths>
#include <QDir>
#include <QDateTime>
#include <QUrl>
#include "DetectionOverlay.h"
#include "OpenCVVisionEngine.h"
#include "VideoFrameCapture.h"

/**
 * @brief AI图像识别平台主窗口
 * 
 * 提供摄像头控制、图像捕获、视频录制和AI识别功能
 */
class MainWindow : public QMainWindow {
    Q_OBJECT

public:
    explicit MainWindow(QWidget* parent = nullptr);
    ~MainWindow() override = default;

private slots:
    void openCamera();
    void closeCamera();
    void startInference();
    void captureImage();
    void saveImageAs();
    void toggleRecording();
    void startRecording();
    void stopRecording();
    void updatePerformanceDisplay();
    void switchVisionMode();
    void onOpenCVDetectionReady(const std::vector<OpenCVDetection>& detections, const QImage& image);
    void onOpenCVStatsUpdated(const OpenCVVisionEngine::ProcessingStats& stats);

private:
    void setupUI();
    void setupCamera();
    void setupImageCapture();
    void setupVideoRecording();
    void setupPerformanceMonitor();
    void setupOpenCVVision();
    void setupFrameCapture();
    QImage createTestImage();

private:
    // 多媒体组件
    QCamera* camera;
    QMediaCaptureSession* captureSession;
    QVideoWidget* videoWidget;
    QImageCapture* imageCapture;
    QMediaRecorder* mediaRecorder;
    
    // UI组件
    QPushButton* openCameraButton;
    QPushButton* startInferenceButton;
    QPushButton* recordButton;
    QTextEdit* performanceText;
    DetectionOverlay* detectionOverlay;

    // OpenCV组件
    OpenCVVisionEngine* openCVEngine;
    QTimer* processingTimer;

    // 视频帧捕获
    VideoFrameCapture* frameCapture;

    // 检测结果显示窗口
    QLabel* detectionImageLabel;

    // 状态
    bool isInferenceRunning = false;
};
