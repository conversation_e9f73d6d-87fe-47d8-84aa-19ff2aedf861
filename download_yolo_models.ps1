# YOLO模型下载脚本
# 下载预训练的YOLO模型文件用于物体检测

param(
    [string]$ModelsDir = "models",
    [switch]$Force = $false
)

Write-Host "=== YOLO模型下载脚本 ===" -ForegroundColor Green
Write-Host "目标目录: $ModelsDir" -ForegroundColor Yellow

# 创建模型目录结构
$yoloDir = Join-Path $ModelsDir "yolo"
$yolov5Dir = Join-Path $yoloDir "yolov5"
$yolov8Dir = Join-Path $yoloDir "yolov8"

Write-Host "创建目录结构..." -ForegroundColor Yellow
New-Item -ItemType Directory -Path $yoloDir -Force | Out-Null
New-Item -ItemType Directory -Path $yolov5Dir -Force | Out-Null
New-Item -ItemType Directory -Path $yolov8Dir -Force | Out-Null

# YOLO模型下载URL
$models = @{
    "YOLOv5s" = @{
        "url" = "https://github.com/ultralytics/yolov5/releases/download/v7.0/yolov5s.onnx"
        "path" = Join-Path $yolov5Dir "yolov5s.onnx"
        "description" = "YOLOv5s ONNX模型 (小型，快速)"
    }
    "YOLOv5m" = @{
        "url" = "https://github.com/ultralytics/yolov5/releases/download/v7.0/yolov5m.onnx"
        "path" = Join-Path $yolov5Dir "yolov5m.onnx"
        "description" = "YOLOv5m ONNX模型 (中型，平衡)"
    }
    "YOLOv8s" = @{
        "url" = "https://github.com/ultralytics/ultralytics/releases/download/v8.0.0/yolov8s.onnx"
        "path" = Join-Path $yolov8Dir "yolov8s.onnx"
        "description" = "YOLOv8s ONNX模型 (小型，最新)"
    }
    "YOLOv8m" = @{
        "url" = "https://github.com/ultralytics/ultralytics/releases/download/v8.0.0/yolov8m.onnx"
        "path" = Join-Path $yolov8Dir "yolov8m.onnx"
        "description" = "YOLOv8m ONNX模型 (中型，最新)"
    }
}

# 下载函数
function Download-Model {
    param($name, $info)
    
    $filePath = $info.path
    $url = $info.url
    $description = $info.description
    
    if ((Test-Path $filePath) -and -not $Force) {
        Write-Host "✓ $name 已存在: $filePath" -ForegroundColor Green
        return $true
    }
    
    Write-Host "下载 $name..." -ForegroundColor Yellow
    Write-Host "  描述: $description" -ForegroundColor Gray
    Write-Host "  URL: $url" -ForegroundColor Gray
    Write-Host "  保存到: $filePath" -ForegroundColor Gray
    
    try {
        # 使用Invoke-WebRequest下载
        $ProgressPreference = 'SilentlyContinue'
        Invoke-WebRequest -Uri $url -OutFile $filePath -UseBasicParsing
        
        if (Test-Path $filePath) {
            $fileSize = (Get-Item $filePath).Length / 1MB
            Write-Host "✓ $name 下载完成 (${fileSize:F1} MB)" -ForegroundColor Green
            return $true
        } else {
            Write-Host "✗ $name 下载失败" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "✗ $name 下载出错: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 开始下载
Write-Host "`n开始下载YOLO模型..." -ForegroundColor Green

$successCount = 0
$totalCount = $models.Count

foreach ($modelName in $models.Keys) {
    $modelInfo = $models[$modelName]
    if (Download-Model -name $modelName -info $modelInfo) {
        $successCount++
    }
    Write-Host ""
}

# 创建模型配置文件
$configPath = Join-Path $yoloDir "model_config.json"
$config = @{
    default_model = "yolov5s"
    models = @{
        yolov5s = @{
            path = "yolo/yolov5/yolov5s.onnx"
            input_size = 640
            confidence_threshold = 0.25
            nms_threshold = 0.45
            description = "YOLOv5s - 快速检测模型"
        }
        yolov5m = @{
            path = "yolo/yolov5/yolov5m.onnx"
            input_size = 640
            confidence_threshold = 0.25
            nms_threshold = 0.45
            description = "YOLOv5m - 平衡性能模型"
        }
        yolov8s = @{
            path = "yolo/yolov8/yolov8s.onnx"
            input_size = 640
            confidence_threshold = 0.25
            nms_threshold = 0.45
            description = "YOLOv8s - 最新快速模型"
        }
        yolov8m = @{
            path = "yolo/yolov8/yolov8m.onnx"
            input_size = 640
            confidence_threshold = 0.25
            nms_threshold = 0.45
            description = "YOLOv8m - 最新平衡模型"
        }
    }
}

$configJson = $config | ConvertTo-Json -Depth 4
$configJson | Out-File -FilePath $configPath -Encoding UTF8

Write-Host "=== 下载完成 ===" -ForegroundColor Green
Write-Host "成功下载: $successCount/$totalCount 个模型" -ForegroundColor Yellow
Write-Host "模型配置文件: $configPath" -ForegroundColor Yellow
Write-Host "COCO标签文件: models/coco_labels.txt" -ForegroundColor Yellow

if ($successCount -gt 0) {
    Write-Host "`n✓ YOLO模型准备完成！可以开始集成到OpenCVVisionEngine中。" -ForegroundColor Green
} else {
    Write-Host "`n✗ 没有成功下载任何模型，请检查网络连接。" -ForegroundColor Red
}

Write-Host "`n使用方法:" -ForegroundColor Cyan
Write-Host "  .\download_yolo_models.ps1                # 下载到默认models目录" -ForegroundColor Gray
Write-Host "  .\download_yolo_models.ps1 -Force         # 强制重新下载" -ForegroundColor Gray
Write-Host "  .\download_yolo_models.ps1 -ModelsDir D:\models  # 指定目录" -ForegroundColor Gray
